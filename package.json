{"name": "cyber-wolf-website", "version": "1.0.0", "description": "Cyber Wolf - Free Website Penetration Testing Class", "main": "index.tsx", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "serve": "vite preview", "type-check": "tsc --noEmit", "lint": "eslint . --ext .ts,.tsx", "format": "prettier --write .", "start": "npm run dev"}, "dependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.8.3"}, "devDependencies": {"@vitejs/plugin-react": "^4.6.0", "vite": "^5.4.19"}, "keywords": ["cybersecurity", "penetration-testing", "web-security", "training", "react", "typescript"], "author": "Cyber Wolf Team", "license": "MIT", "homepage": "https://cyber-wolf-free-training.web.app/", "repository": {"type": "git", "url": "https://github.com/cyber-wolf/training-website.git"}}